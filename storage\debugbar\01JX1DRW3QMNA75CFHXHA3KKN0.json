{"__meta": {"id": "01JX1DRW3QMNA75CFHXHA3KKN0", "datetime": "2025-06-06 09:30:32", "utime": **********.44057, "method": "GET", "uri": "/student-fees/1?limit=10&sort=id&order=desc&offset=0&search=&start_date=&end_date=&show_deleted=0", "ip": "127.0.0.1"}, "messages": {"count": 8, "messages": [{"message": "[09:30:31] LOG.warning: Optional parameter $includeSignature declared before required parameter $invoiceTypeCode is implicitly treated as a required parameter in D:\\laragon\\www\\schola\\app\\Services\\EInvoiceFormatService.php on line 748", "message_html": null, "is_string": false, "label": "warning", "time": **********.487216, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:32] LOG.warning: strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated in D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php on line 928", "message_html": null, "is_string": false, "label": "warning", "time": **********.259469, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:32] LOG.warning: strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated in D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php on line 928", "message_html": null, "is_string": false, "label": "warning", "time": **********.274957, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:32] LOG.warning: strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated in D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php on line 928", "message_html": null, "is_string": false, "label": "warning", "time": **********.303185, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:32] LOG.warning: strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated in D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php on line 928", "message_html": null, "is_string": false, "label": "warning", "time": **********.318347, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:32] LOG.warning: strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated in D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php on line 928", "message_html": null, "is_string": false, "label": "warning", "time": **********.389893, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:32] LOG.warning: strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated in D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php on line 928", "message_html": null, "is_string": false, "label": "warning", "time": **********.405887, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:32] LOG.warning: strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated in D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php on line 928", "message_html": null, "is_string": false, "label": "warning", "time": **********.424717, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.187415, "end": **********.440622, "duration": 1.2532072067260742, "duration_str": "1.25s", "measures": [{"label": "Booting", "start": **********.187415, "relative_start": 0, "end": **********.434127, "relative_end": **********.434127, "duration": 0.*****************, "duration_str": "247ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.434145, "relative_start": 0.*****************, "end": **********.440626, "relative_end": 3.814697265625e-06, "duration": 1.****************, "duration_str": "1.01s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.455736, "relative_start": 0.*****************, "end": **********.463669, "relative_end": **********.463669, "duration": 0.*****************, "duration_str": "7.93ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.438028, "relative_start": 1.****************, "end": **********.438434, "relative_end": **********.438434, "duration": 0.0004057884216308594, "duration_str": "406μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "90MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.10", "Environment": "local", "Debug Mode": "Enabled", "URL": "schola.one", "Timezone": "Asia/Kuala_Lumpur", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 103, "nb_statements": 103, "nb_visible_statements": 103, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.13719999999999993, "accumulated_duration_str": "137ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 3 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `users` where `id` = 2 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.500369, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "schola", "explain": null, "start_percent": 0, "width_percent": 0.343}, {"sql": "select * from `schools` where `schools`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 13}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "Role", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckRole.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.507189, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "checkSchoolStatus:13", "source": {"index": 22, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FMiddleware%2FCheckSchoolStatus.php&line=13", "ajax": false, "filename": "CheckSchoolStatus.php", "line": "13"}, "connection": "schola", "explain": null, "start_percent": 0.343, "width_percent": 0.299}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (2) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 27}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "Role", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckRole.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.513229, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "schola", "explain": null, "start_percent": 0.641, "width_percent": 0.525}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 304}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 174}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 175}], "start": **********.5199971, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:266", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=266", "ajax": false, "filename": "PermissionRegistrar.php", "line": "266"}, "connection": "schola", "explain": null, "start_percent": 1.166, "width_percent": 0.758}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 613, 1259, 1260, 1261, 1262, 1263, 1265, 1266, 1267, 1268, 1269, 1270, 1271, 1272, 1273)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 304}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 174}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 175}], "start": **********.528342, "duration": 0.04954, "duration_str": "49.54ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:266", "source": {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=266", "ajax": false, "filename": "PermissionRegistrar.php", "line": "266"}, "connection": "schola", "explain": null, "start_percent": 1.924, "width_percent": 36.108}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 2 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [2, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.22168, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "schola", "explain": null, "start_percent": 38.032, "width_percent": 0.554}, {"sql": "select count(*) as aggregate from `student_fees` where `school_id` = 1 and `student_fees`.`school_id` = 1 and (`student_fees`.`deleted_at` is null)", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 804}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2277548, "duration": 0.00637, "duration_str": "6.37ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:804", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 804}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=804", "ajax": false, "filename": "StudentFeesController.php", "line": "804"}, "connection": "schola", "explain": null, "start_percent": 38.586, "width_percent": 4.643}, {"sql": "select `student_fees`.* from `student_fees` where `school_id` = 1 and `student_fees`.`school_id` = 1 and (`student_fees`.`deleted_at` is null) order by `id` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2350512, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:806", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=806", "ajax": false, "filename": "StudentFeesController.php", "line": "806"}, "connection": "schola", "explain": null, "start_percent": 43.229, "width_percent": 0.481}, {"sql": "select * from `students` where `students`.`id` in (1, 83, 4868)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2368302, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:806", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=806", "ajax": false, "filename": "StudentFeesController.php", "line": "806"}, "connection": "schola", "explain": null, "start_percent": 43.71, "width_percent": 0.321}, {"sql": "select * from `users` where `users`.`id` in (5, 131, 10366)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.238835, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:806", "source": {"index": 25, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=806", "ajax": false, "filename": "StudentFeesController.php", "line": "806"}, "connection": "schola", "explain": null, "start_percent": 44.031, "width_percent": 0.612}, {"sql": "select `id`, `name`, `stream_id`, `medium_id` from `classes` where `classes`.`id` in (343, 407)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.241345, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:806", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=806", "ajax": false, "filename": "StudentFeesController.php", "line": "806"}, "connection": "schola", "explain": null, "start_percent": 44.643, "width_percent": 0.394}, {"sql": "select `id`, `name` from `mediums` where `mediums`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.243271, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:806", "source": {"index": 25, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=806", "ajax": false, "filename": "StudentFeesController.php", "line": "806"}, "connection": "schola", "explain": null, "start_percent": 45.036, "width_percent": 0.248}, {"sql": "select * from `student_fees_details` where `student_fees_details`.`student_fees_id` in (14495, 14496, 14497, 14498, 14499, 14500, 14501, 14502, 14504, 14505)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.246516, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:806", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=806", "ajax": false, "filename": "StudentFeesController.php", "line": "806"}, "connection": "schola", "explain": null, "start_percent": 45.284, "width_percent": 0.722}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14505 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14505], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.249215, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 46.006, "width_percent": 0.525}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` = *********", "type": "query", "params": [], "bindings": [1, *********], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.250802, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 46.531, "width_percent": 0.27}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14505 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14505, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.252025, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 46.8, "width_percent": 0.284}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14505 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14505], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2578359, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 47.085, "width_percent": 0.525}, {"sql": "select * from `student_fees` where `id` = 14505 limit 1", "type": "query", "params": [], "bindings": [14505], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.259677, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 47.609, "width_percent": 0.525}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14505", "type": "query", "params": [], "bindings": [14505], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.26136, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 48.134, "width_percent": 0.386}, {"sql": "select sum(`amount`) as aggregate from `student_fees_paids` where `student_fees_id` in (14504, 14502)", "type": "query", "params": [], "bindings": [14504, 14502], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 977}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2627978, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:977", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 977}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=977", "ajax": false, "filename": "StudentFeesController.php", "line": "977"}, "connection": "schola", "explain": null, "start_percent": 48.52, "width_percent": 0.299}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2642019, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1016", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1016", "ajax": false, "filename": "StudentFeesController.php", "line": "1016"}, "connection": "schola", "explain": null, "start_percent": 48.819, "width_percent": 0.306}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.265423, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1045", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1045", "ajax": false, "filename": "StudentFeesController.php", "line": "1045"}, "connection": "schola", "explain": null, "start_percent": 49.125, "width_percent": 0.211}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14504 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14504], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.266576, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 49.337, "width_percent": 0.656}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` = *********", "type": "query", "params": [], "bindings": [1, *********], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.26851, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 49.993, "width_percent": 0.248}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14504 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14504, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.269686, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 50.241, "width_percent": 0.226}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14504 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14504], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.27317, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 50.466, "width_percent": 0.43}, {"sql": "select * from `student_fees` where `id` = 14504 limit 1", "type": "query", "params": [], "bindings": [14504], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.275115, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 50.897, "width_percent": 0.43}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14504", "type": "query", "params": [], "bindings": [14504], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2764919, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 51.327, "width_percent": 0.277}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.277887, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1016", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1016", "ajax": false, "filename": "StudentFeesController.php", "line": "1016"}, "connection": "schola", "explain": null, "start_percent": 51.603, "width_percent": 1.028}, {"sql": "select count(*) as aggregate from `e_invoice_guardian` where `guardian_id` = 10365 and `school_id` = 1 and (`name` is null or `name` = '' or `ic_no` is null or `ic_no` = '' or `tax_identification_number` is null or `tax_identification_number` = '' or `address` is null or `address` = '' or `city` is null or `city` = '' or `postal_code` is null or `postal_code` = '' or `country` is null or `country` = '' or `state` is null or `state` = '')", "type": "query", "params": [], "bindings": [10365, 1, "", "", "", "", "", "", "", ""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2801929, "duration": 0.0030299999999999997, "duration_str": "3.03ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1039", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1039", "ajax": false, "filename": "StudentFeesController.php", "line": "1039"}, "connection": "schola", "explain": null, "start_percent": 52.631, "width_percent": 2.208}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.284206, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1045", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1045", "ajax": false, "filename": "StudentFeesController.php", "line": "1045"}, "connection": "schola", "explain": null, "start_percent": 54.84, "width_percent": 1.042}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14502 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14502], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.286418, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 55.882, "width_percent": 0.415}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` = *********", "type": "query", "params": [], "bindings": [1, *********], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.288117, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 56.297, "width_percent": 0.364}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14502 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14502, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.28956, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 56.662, "width_percent": 0.343}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14502 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14502], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.301245, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 57.004, "width_percent": 0.656}, {"sql": "select * from `student_fees` where `id` = 14502 limit 1", "type": "query", "params": [], "bindings": [14502], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.303347, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 57.66, "width_percent": 0.372}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14502", "type": "query", "params": [], "bindings": [14502], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.304634, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 58.032, "width_percent": 0.255}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 83 limit 1", "type": "query", "params": [], "bindings": [1, 83], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.306015, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1016", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1016", "ajax": false, "filename": "StudentFeesController.php", "line": "1016"}, "connection": "schola", "explain": null, "start_percent": 58.287, "width_percent": 0.27}, {"sql": "select count(*) as aggregate from `e_invoice_guardian` where `guardian_id` = 59 and `school_id` = 1 and (`name` is null or `name` = '' or `ic_no` is null or `ic_no` = '' or `tax_identification_number` is null or `tax_identification_number` = '' or `address` is null or `address` = '' or `city` is null or `city` = '' or `postal_code` is null or `postal_code` = '' or `country` is null or `country` = '' or `state` is null or `state` = '')", "type": "query", "params": [], "bindings": [59, 1, "", "", "", "", "", "", "", ""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3073342, "duration": 0.00275, "duration_str": "2.75ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1039", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1039", "ajax": false, "filename": "StudentFeesController.php", "line": "1039"}, "connection": "schola", "explain": null, "start_percent": 58.557, "width_percent": 2.004}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 83 limit 1", "type": "query", "params": [], "bindings": [1, 83], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.31106, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1045", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1045", "ajax": false, "filename": "StudentFeesController.php", "line": "1045"}, "connection": "schola", "explain": null, "start_percent": 60.561, "width_percent": 0.27}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14501 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14501], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.312222, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 60.831, "width_percent": 0.328}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` = *********", "type": "query", "params": [], "bindings": [1, *********], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.313475, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 61.159, "width_percent": 0.138}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14501 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14501, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.314482, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 61.297, "width_percent": 0.219}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14501 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14501], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.316241, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 61.516, "width_percent": 0.765}, {"sql": "select * from `student_fees` where `id` = 14501 limit 1", "type": "query", "params": [], "bindings": [14501], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.318501, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 62.281, "width_percent": 0.364}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14501", "type": "query", "params": [], "bindings": [14501], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.319814, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 62.646, "width_percent": 0.27}, {"sql": "select sum(`amount`) as aggregate from `student_fees_paids` where `student_fees_id` in (14170, 14148)", "type": "query", "params": [], "bindings": [14170, 14148], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 977}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.321097, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:977", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 977}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=977", "ajax": false, "filename": "StudentFeesController.php", "line": "977"}, "connection": "schola", "explain": null, "start_percent": 62.915, "width_percent": 0.27}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.322797, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1016", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1016", "ajax": false, "filename": "StudentFeesController.php", "line": "1016"}, "connection": "schola", "explain": null, "start_percent": 63.185, "width_percent": 0.379}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.324393, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1045", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1045", "ajax": false, "filename": "StudentFeesController.php", "line": "1045"}, "connection": "schola", "explain": null, "start_percent": 63.564, "width_percent": 0.27}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14500 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14500], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.325561, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 63.834, "width_percent": 0.284}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` = *********", "type": "query", "params": [], "bindings": [1, *********], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3267581, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 64.118, "width_percent": 0.211}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14500 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14500, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3278918, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 64.329, "width_percent": 0.299}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14500 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14500], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.33119, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 64.628, "width_percent": 0.481}, {"sql": "select * from `student_fees` where `id` = 14500 limit 1", "type": "query", "params": [], "bindings": [14500], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3328319, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 65.109, "width_percent": 0.313}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14500", "type": "query", "params": [], "bindings": [14500], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.334044, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 65.423, "width_percent": 0.262}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3354988, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1016", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1016", "ajax": false, "filename": "StudentFeesController.php", "line": "1016"}, "connection": "schola", "explain": null, "start_percent": 65.685, "width_percent": 1.72}, {"sql": "select count(*) as aggregate from `e_invoice_guardian` where `guardian_id` = 10365 and `school_id` = 1 and (`name` is null or `name` = '' or `ic_no` is null or `ic_no` = '' or `tax_identification_number` is null or `tax_identification_number` = '' or `address` is null or `address` = '' or `city` is null or `city` = '' or `postal_code` is null or `postal_code` = '' or `country` is null or `country` = '' or `state` is null or `state` = '')", "type": "query", "params": [], "bindings": [10365, 1, "", "", "", "", "", "", "", ""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.338973, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1039", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1039", "ajax": false, "filename": "StudentFeesController.php", "line": "1039"}, "connection": "schola", "explain": null, "start_percent": 67.405, "width_percent": 1.786}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.342377, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1045", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1045", "ajax": false, "filename": "StudentFeesController.php", "line": "1045"}, "connection": "schola", "explain": null, "start_percent": 69.191, "width_percent": 1.297}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14499 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14499], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.345128, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 70.488, "width_percent": 0.343}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` = *********", "type": "query", "params": [], "bindings": [1, *********], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3464482, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 70.831, "width_percent": 0.197}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14499 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14499, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.347533, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 71.028, "width_percent": 0.277}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14499 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14499], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3507972, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 71.305, "width_percent": 0.474}, {"sql": "select * from `student_fees` where `id` = 14499 limit 1", "type": "query", "params": [], "bindings": [14499], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.352458, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 71.778, "width_percent": 0.284}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14499", "type": "query", "params": [], "bindings": [14499], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.353642, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 72.063, "width_percent": 0.248}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.355042, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1016", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1016", "ajax": false, "filename": "StudentFeesController.php", "line": "1016"}, "connection": "schola", "explain": null, "start_percent": 72.31, "width_percent": 1.115}, {"sql": "select count(*) as aggregate from `e_invoice_guardian` where `guardian_id` = 10365 and `school_id` = 1 and (`name` is null or `name` = '' or `ic_no` is null or `ic_no` = '' or `tax_identification_number` is null or `tax_identification_number` = '' or `address` is null or `address` = '' or `city` is null or `city` = '' or `postal_code` is null or `postal_code` = '' or `country` is null or `country` = '' or `state` is null or `state` = '')", "type": "query", "params": [], "bindings": [10365, 1, "", "", "", "", "", "", "", ""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3577719, "duration": 0.00271, "duration_str": "2.71ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1039", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1039", "ajax": false, "filename": "StudentFeesController.php", "line": "1039"}, "connection": "schola", "explain": null, "start_percent": 73.426, "width_percent": 1.975}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3614671, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1045", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1045", "ajax": false, "filename": "StudentFeesController.php", "line": "1045"}, "connection": "schola", "explain": null, "start_percent": 75.401, "width_percent": 1.13}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14498 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14498], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.363993, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 76.531, "width_percent": 0.539}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` = *********", "type": "query", "params": [], "bindings": [1, *********], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3657188, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 77.07, "width_percent": 0.284}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14498 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14498, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.366943, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 77.354, "width_percent": 0.306}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14498 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14498], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.369811, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 77.66, "width_percent": 0.364}, {"sql": "select * from `student_fees` where `id` = 14498 limit 1", "type": "query", "params": [], "bindings": [14498], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3716679, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 78.025, "width_percent": 0.445}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14498", "type": "query", "params": [], "bindings": [14498], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3731399, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 78.469, "width_percent": 0.27}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.374542, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1016", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1016", "ajax": false, "filename": "StudentFeesController.php", "line": "1016"}, "connection": "schola", "explain": null, "start_percent": 78.739, "width_percent": 1.115}, {"sql": "select count(*) as aggregate from `e_invoice_guardian` where `guardian_id` = 10365 and `school_id` = 1 and (`name` is null or `name` = '' or `ic_no` is null or `ic_no` = '' or `tax_identification_number` is null or `tax_identification_number` = '' or `address` is null or `address` = '' or `city` is null or `city` = '' or `postal_code` is null or `postal_code` = '' or `country` is null or `country` = '' or `state` is null or `state` = '')", "type": "query", "params": [], "bindings": [10365, 1, "", "", "", "", "", "", "", ""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.376992, "duration": 0.0027400000000000002, "duration_str": "2.74ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1039", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1039", "ajax": false, "filename": "StudentFeesController.php", "line": "1039"}, "connection": "schola", "explain": null, "start_percent": 79.854, "width_percent": 1.997}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.380685, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1045", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1045", "ajax": false, "filename": "StudentFeesController.php", "line": "1045"}, "connection": "schola", "explain": null, "start_percent": 81.851, "width_percent": 1.108}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14497 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14497], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.382986, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 82.959, "width_percent": 0.379}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3843198, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 83.338, "width_percent": 0.182}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14497 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14497, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.385652, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 83.52, "width_percent": 0.466}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14497 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14497], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.388743, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 83.987, "width_percent": 0.292}, {"sql": "select * from `student_fees` where `id` = 14497 limit 1", "type": "query", "params": [], "bindings": [14497], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.390001, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 84.278, "width_percent": 0.262}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14497", "type": "query", "params": [], "bindings": [14497], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.39107, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 84.541, "width_percent": 0.262}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.392677, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1016", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1016", "ajax": false, "filename": "StudentFeesController.php", "line": "1016"}, "connection": "schola", "explain": null, "start_percent": 84.803, "width_percent": 0.474}, {"sql": "select count(*) as aggregate from `e_invoice_guardian` where `guardian_id` = 8 and `school_id` = 1 and (`name` is null or `name` = '' or `ic_no` is null or `ic_no` = '' or `tax_identification_number` is null or `tax_identification_number` = '' or `address` is null or `address` = '' or `city` is null or `city` = '' or `postal_code` is null or `postal_code` = '' or `country` is null or `country` = '' or `state` is null or `state` = '')", "type": "query", "params": [], "bindings": [8, 1, "", "", "", "", "", "", "", ""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3942819, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1039", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1039", "ajax": false, "filename": "StudentFeesController.php", "line": "1039"}, "connection": "schola", "explain": null, "start_percent": 85.277, "width_percent": 1.706}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3974342, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1045", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1045", "ajax": false, "filename": "StudentFeesController.php", "line": "1045"}, "connection": "schola", "explain": null, "start_percent": 86.983, "width_percent": 0.321}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14496 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14496], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3988879, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 87.303, "width_percent": 0.576}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` = *********", "type": "query", "params": [], "bindings": [1, *********], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.400615, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 87.879, "width_percent": 0.255}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14496 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14496, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.401762, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 88.134, "width_percent": 0.284}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14496 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14496], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.404481, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 88.418, "width_percent": 0.292}, {"sql": "select * from `student_fees` where `id` = 14496 limit 1", "type": "query", "params": [], "bindings": [14496], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4060922, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 88.71, "width_percent": 0.459}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14496", "type": "query", "params": [], "bindings": [14496], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.407598, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 89.169, "width_percent": 0.284}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.408981, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1016", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1016", "ajax": false, "filename": "StudentFeesController.php", "line": "1016"}, "connection": "schola", "explain": null, "start_percent": 89.453, "width_percent": 1.122}, {"sql": "select count(*) as aggregate from `e_invoice_guardian` where `guardian_id` = 10365 and `school_id` = 1 and (`name` is null or `name` = '' or `ic_no` is null or `ic_no` = '' or `tax_identification_number` is null or `tax_identification_number` = '' or `address` is null or `address` = '' or `city` is null or `city` = '' or `postal_code` is null or `postal_code` = '' or `country` is null or `country` = '' or `state` is null or `state` = '')", "type": "query", "params": [], "bindings": [10365, 1, "", "", "", "", "", "", "", ""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4114208, "duration": 0.00304, "duration_str": "3.04ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1039", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1039", "ajax": false, "filename": "StudentFeesController.php", "line": "1039"}, "connection": "schola", "explain": null, "start_percent": 90.576, "width_percent": 2.216}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.415397, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1045", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1045", "ajax": false, "filename": "StudentFeesController.php", "line": "1045"}, "connection": "schola", "explain": null, "start_percent": 92.792, "width_percent": 1.086}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14495 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14495], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.417653, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 93.878, "width_percent": 0.262}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` = *********", "type": "query", "params": [], "bindings": [1, *********], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.418773, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 94.14, "width_percent": 0.248}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14495 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14495, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.42023, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 94.388, "width_percent": 0.437}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14495 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14495], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4234102, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 94.825, "width_percent": 0.335}, {"sql": "select * from `student_fees` where `id` = 14495 limit 1", "type": "query", "params": [], "bindings": [14495], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.42483, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 95.16, "width_percent": 0.277}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14495", "type": "query", "params": [], "bindings": [14495], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.426033, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 95.437, "width_percent": 0.299}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = ? and `students`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.427811, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 95.736, "width_percent": 1.268}, {"sql": "select count(*) as aggregate from `e_invoice_guardian` where `guardian_id` = ? and `school_id` = ? and (`name` is null or `name` = ? or `ic_no` is null or `ic_no` = ? or `tax_identification_number` is null or `tax_identification_number` = ? or `address` is null or `address` = ? or `city` is null or `city` = ? or `postal_code` is null or `postal_code` = ? or `country` is null or `country` = ? or `state` is null or `state` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4298272, "duration": 0.0023799999999999997, "duration_str": "2.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 97.004, "width_percent": 1.735}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = ? and `students`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.432435, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 98.739, "width_percent": 1.261}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 13675, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 227, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\StudentFeesDetail": {"value": 24, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FStudentFeesDetail.php&line=1", "ajax": false, "filename": "StudentFeesDetail.php", "line": "?"}}, "App\\Models\\StudentFee": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FStudentFee.php&line=1", "ajax": false, "filename": "StudentFee.php", "line": "?"}}, "App\\Models\\User": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Students": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FStudents.php&line=1", "ajax": false, "filename": "Students.php", "line": "?"}}, "App\\Models\\ClassSchool": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FClassSchool.php&line=1", "ajax": false, "filename": "ClassSchool.php", "line": "?"}}, "App\\Models\\School": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FSchool.php&line=1", "ajax": false, "filename": "School.php", "line": "?"}}, "App\\Models\\Mediums": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FMediums.php&line=1", "ajax": false, "filename": "Mediums.php", "line": "?"}}}, "count": 13947, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => fees-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-73607012 data-indent-pad=\"  \"><span class=sf-dump-note>fees-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">fees-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-73607012\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.226689, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "https://schola.test/student-fees/1?end_date=&limit=10&offset=0&order=desc&search=&show_deleted=0&sor...", "action_name": "student-fees.show", "controller_action": "App\\Http\\Controllers\\StudentFeesController@show", "uri": "GET student-fees/{student_fee}", "controller": "App\\Http\\Controllers\\StudentFeesController@show<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=476\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=476\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/StudentFeesController.php:476-1054</a>", "middleware": "web, Role, auth, checkSchoolStatus, status, language", "duration": "1.26s", "peak_memory": "94MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1666061250 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>sort</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n  \"<span class=sf-dump-key>order</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n  \"<span class=sf-dump-key>offset</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>start_date</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>end_date</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>show_deleted</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1666061250\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1482198731 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1482198731\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1801495601 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">schola.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">qzZAZCbyxouAflTIwxgtbSqzkAL1GsMdrfVvou3B</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">https://schola.test/student-fees</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"742 characters\">XSRF-TOKEN=eyJpdiI6Im5lZGJiUFdMUTZ6cHZWOUNwS2F0MGc9PSIsInZhbHVlIjoiOGU3NVhNVENXQjRuRkUrZndqVElTUVFvSHo4emxFZDEwRXFySVhxTEN2UDg0VGE4QitveXozNlZsNFFoVEpUU3Q5MUpLVTR4WFpyM0hmTkJmQ3NwOFFPdzNpang3KzZEWFd0dlRVSDkzUHR5TVA0MTdyd2J1RDRyNk4wQzlpbk8iLCJtYWMiOiI2NDYwM2Q5YmM2ZDZkMzBiMDMwMjkyZWI5MWE4Njk0NzU4N2MxZDZlNGQwMmJmOTdiMWQ2YjljNDgzYTY3MzAxIiwidGFnIjoiIn0%3D; schola_saas_school_management_system_session=eyJpdiI6IkZoMnFjMm9KbHZtdWpDYlhPcW05Q0E9PSIsInZhbHVlIjoiSmhTSXY3aHkzYVFxYlJFeDQyWlhJeUluM1k3bHdFdGtEenViaUIvdU1OZEVqZ1hCYTdoU0sxbm9QSS9zWTAwSzhmY3VnVUY4SW5wcWJwQ0pKTms2Nnp0YmltQ3dCc2U5YVp3UURSRUFKclV3VzNyTktpclF5S1V2eXZJNkNZK3EiLCJtYWMiOiIxYmFiYWM1M2E3NjIzMTU2YzJlNTExZTlmZWUwODI4MWViYjUyZDFiODIzOWRkZTdkYmEwODJiYzMwOGU5ZDI0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1801495601\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-264659726 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qzZAZCbyxouAflTIwxgtbSqzkAL1GsMdrfVvou3B</span>\"\n  \"<span class=sf-dump-key>schola_saas_school_management_system_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ngwskcSF7OPJhEVQnobXQuptusCBAMo2pYQsya27</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-264659726\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1268638433 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 01:30:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1268638433\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-85689360 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qzZAZCbyxouAflTIwxgtbSqzkAL1GsMdrfVvou3B</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"63 characters\">https://schola.test/student-fees/paid/receipt-pdf/14505/invoice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>landing_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>language</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Language\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Language</span></span> {<a class=sf-dump-ref>#3448</a><samp data-depth=2 class=sf-dump-compact>\n    #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">languages</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n    +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n    #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n    +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n    +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n    +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">English</span>\"\n      \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"7 characters\">en.json</span>\"\n      \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>is_rtl</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n      \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">English</span>\"\n      \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"7 characters\">en.json</span>\"\n      \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>is_rtl</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n      \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n    +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n    +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">code</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">is_rtl</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n    </samp>]\n  </samp>}\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1749172762</span>\n  </samp>]\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-85689360\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://schola.test/student-fees/1?end_date=&limit=10&offset=0&order=desc&search=&show_deleted=0&sor...", "action_name": "student-fees.show", "controller_action": "App\\Http\\Controllers\\StudentFeesController@show"}, "badge": null}}