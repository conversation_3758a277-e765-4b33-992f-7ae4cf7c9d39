{"__meta": {"id": "01JX1DRMBH10KWFRJ5N1R85N58", "datetime": "2025-06-06 09:30:24", "utime": **********.498261, "method": "GET", "uri": "/student-fees/1?limit=10&sort=id&order=desc&offset=0&search=&start_date=&end_date=&show_deleted=0", "ip": "127.0.0.1"}, "messages": {"count": 8, "messages": [{"message": "[09:30:23] LOG.warning: Optional parameter $includeSignature declared before required parameter $invoiceTypeCode is implicitly treated as a required parameter in D:\\laragon\\www\\schola\\app\\Services\\EInvoiceFormatService.php on line 748", "message_html": null, "is_string": false, "label": "warning", "time": **********.336528, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:24] LOG.warning: strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated in D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php on line 928", "message_html": null, "is_string": false, "label": "warning", "time": **********.256754, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:24] LOG.warning: strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated in D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php on line 928", "message_html": null, "is_string": false, "label": "warning", "time": **********.280662, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:24] LOG.warning: strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated in D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php on line 928", "message_html": null, "is_string": false, "label": "warning", "time": **********.321237, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:24] LOG.warning: strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated in D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php on line 928", "message_html": null, "is_string": false, "label": "warning", "time": **********.340036, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:24] LOG.warning: strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated in D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php on line 928", "message_html": null, "is_string": false, "label": "warning", "time": **********.43296, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:24] LOG.warning: strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated in D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php on line 928", "message_html": null, "is_string": false, "label": "warning", "time": **********.452705, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:24] LOG.warning: strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated in D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php on line 928", "message_html": null, "is_string": false, "label": "warning", "time": **********.474535, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1749173422.902946, "end": **********.498292, "duration": 1.5953459739685059, "duration_str": "1.6s", "measures": [{"label": "Booting", "start": 1749173422.902946, "relative_start": 0, "end": **********.27459, "relative_end": **********.27459, "duration": 0.****************, "duration_str": "372ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.274609, "relative_start": 0.*****************, "end": **********.498294, "relative_end": 2.1457672119140625e-06, "duration": 1.****************, "duration_str": "1.22s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.299338, "relative_start": 0.****************, "end": **********.308442, "relative_end": **********.308442, "duration": 0.009104013442993164, "duration_str": "9.1ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.492775, "relative_start": 1.****************, "end": **********.493901, "relative_end": **********.493901, "duration": 0.0011260509490966797, "duration_str": "1.13ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "90MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.10", "Environment": "local", "Debug Mode": "Enabled", "URL": "schola.one", "Timezone": "Asia/Kuala_Lumpur", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 103, "nb_statements": 103, "nb_visible_statements": 103, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.16782999999999998, "accumulated_duration_str": "168ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 3 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `users` where `id` = 2 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.353934, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "schola", "explain": null, "start_percent": 0, "width_percent": 0.524}, {"sql": "select * from `schools` where `schools`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 13}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "Role", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckRole.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3616831, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "checkSchoolStatus:13", "source": {"index": 22, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FMiddleware%2FCheckSchoolStatus.php&line=13", "ajax": false, "filename": "CheckSchoolStatus.php", "line": "13"}, "connection": "schola", "explain": null, "start_percent": 0.524, "width_percent": 0.298}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (2) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 27}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "Role", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckRole.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.369011, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "schola", "explain": null, "start_percent": 0.822, "width_percent": 0.441}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 304}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 174}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 175}], "start": **********.377771, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:266", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=266", "ajax": false, "filename": "PermissionRegistrar.php", "line": "266"}, "connection": "schola", "explain": null, "start_percent": 1.263, "width_percent": 1.049}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 613, 1259, 1260, 1261, 1262, 1263, 1265, 1266, 1267, 1268, 1269, 1270, 1271, 1272, 1273)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 304}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 174}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 175}], "start": **********.3877602, "duration": 0.05439, "duration_str": "54.39ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:266", "source": {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=266", "ajax": false, "filename": "PermissionRegistrar.php", "line": "266"}, "connection": "schola", "explain": null, "start_percent": 2.312, "width_percent": 32.408}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 2 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [2, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.200635, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "schola", "explain": null, "start_percent": 34.72, "width_percent": 0.566}, {"sql": "select count(*) as aggregate from `student_fees` where `school_id` = 1 and `student_fees`.`school_id` = 1 and (`student_fees`.`deleted_at` is null)", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 804}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.209065, "duration": 0.00728, "duration_str": "7.28ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:804", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 804}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=804", "ajax": false, "filename": "StudentFeesController.php", "line": "804"}, "connection": "schola", "explain": null, "start_percent": 35.286, "width_percent": 4.338}, {"sql": "select `student_fees`.* from `student_fees` where `school_id` = 1 and `student_fees`.`school_id` = 1 and (`student_fees`.`deleted_at` is null) order by `id` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.217645, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:806", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=806", "ajax": false, "filename": "StudentFeesController.php", "line": "806"}, "connection": "schola", "explain": null, "start_percent": 39.623, "width_percent": 0.763}, {"sql": "select * from `students` where `students`.`id` in (1, 83, 4868)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.221003, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:806", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=806", "ajax": false, "filename": "StudentFeesController.php", "line": "806"}, "connection": "schola", "explain": null, "start_percent": 40.386, "width_percent": 0.548}, {"sql": "select * from `users` where `users`.`id` in (5, 131, 10366)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.223937, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:806", "source": {"index": 25, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=806", "ajax": false, "filename": "StudentFeesController.php", "line": "806"}, "connection": "schola", "explain": null, "start_percent": 40.934, "width_percent": 0.435}, {"sql": "select `id`, `name`, `stream_id`, `medium_id` from `classes` where `classes`.`id` in (343, 407)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2279599, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:806", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=806", "ajax": false, "filename": "StudentFeesController.php", "line": "806"}, "connection": "schola", "explain": null, "start_percent": 41.369, "width_percent": 0.673}, {"sql": "select `id`, `name` from `mediums` where `mediums`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2311451, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:806", "source": {"index": 25, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=806", "ajax": false, "filename": "StudentFeesController.php", "line": "806"}, "connection": "schola", "explain": null, "start_percent": 42.043, "width_percent": 0.59}, {"sql": "select * from `student_fees_details` where `student_fees_details`.`student_fees_id` in (14495, 14496, 14497, 14498, 14499, 14500, 14501, 14502, 14504, 14505)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.236323, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:806", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=806", "ajax": false, "filename": "StudentFeesController.php", "line": "806"}, "connection": "schola", "explain": null, "start_percent": 42.632, "width_percent": 0.626}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14505 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14505], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.240227, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 43.258, "width_percent": 0.679}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` = *********", "type": "query", "params": [], "bindings": [1, *********], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.242644, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 43.937, "width_percent": 0.495}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14505 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14505, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2446241, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 44.432, "width_percent": 0.375}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14505 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14505], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2550251, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 44.807, "width_percent": 0.399}, {"sql": "select * from `student_fees` where `id` = 14505 limit 1", "type": "query", "params": [], "bindings": [14505], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.256952, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 45.206, "width_percent": 0.375}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14505", "type": "query", "params": [], "bindings": [14505], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.258562, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 45.582, "width_percent": 0.292}, {"sql": "select sum(`amount`) as aggregate from `student_fees_paids` where `student_fees_id` in (14504, 14502)", "type": "query", "params": [], "bindings": [14504, 14502], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 977}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.260919, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:977", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 977}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=977", "ajax": false, "filename": "StudentFeesController.php", "line": "977"}, "connection": "schola", "explain": null, "start_percent": 45.874, "width_percent": 0.518}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.263479, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1016", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1016", "ajax": false, "filename": "StudentFeesController.php", "line": "1016"}, "connection": "schola", "explain": null, "start_percent": 46.392, "width_percent": 0.483}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.265659, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1045", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1045", "ajax": false, "filename": "StudentFeesController.php", "line": "1045"}, "connection": "schola", "explain": null, "start_percent": 46.875, "width_percent": 0.787}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14504 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14504], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.268834, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 47.661, "width_percent": 0.822}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` = *********", "type": "query", "params": [], "bindings": [1, *********], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.271649, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 48.484, "width_percent": 0.322}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14504 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14504, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.273477, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 48.805, "width_percent": 0.715}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14504 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14504], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.278641, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 49.52, "width_percent": 0.387}, {"sql": "select * from `student_fees` where `id` = 14504 limit 1", "type": "query", "params": [], "bindings": [14504], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2809958, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 49.908, "width_percent": 0.459}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14504", "type": "query", "params": [], "bindings": [14504], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2828941, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 50.366, "width_percent": 0.369}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.284911, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1016", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1016", "ajax": false, "filename": "StudentFeesController.php", "line": "1016"}, "connection": "schola", "explain": null, "start_percent": 50.736, "width_percent": 1.078}, {"sql": "select count(*) as aggregate from `e_invoice_guardian` where `guardian_id` = 10365 and `school_id` = 1 and (`name` is null or `name` = '' or `ic_no` is null or `ic_no` = '' or `tax_identification_number` is null or `tax_identification_number` = '' or `address` is null or `address` = '' or `city` is null or `city` = '' or `postal_code` is null or `postal_code` = '' or `country` is null or `country` = '' or `state` is null or `state` = '')", "type": "query", "params": [], "bindings": [10365, 1, "", "", "", "", "", "", "", ""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2886949, "duration": 0.0035600000000000002, "duration_str": "3.56ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1039", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1039", "ajax": false, "filename": "StudentFeesController.php", "line": "1039"}, "connection": "schola", "explain": null, "start_percent": 51.814, "width_percent": 2.121}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.29349, "duration": 0.00268, "duration_str": "2.68ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1045", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1045", "ajax": false, "filename": "StudentFeesController.php", "line": "1045"}, "connection": "schola", "explain": null, "start_percent": 53.936, "width_percent": 1.597}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14502 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14502], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.297694, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 55.532, "width_percent": 0.614}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` = *********", "type": "query", "params": [], "bindings": [1, *********], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.299714, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 56.146, "width_percent": 0.22}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14502 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14502, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.301115, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 56.367, "width_percent": 0.453}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14502 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14502], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.319761, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 56.819, "width_percent": 0.316}, {"sql": "select * from `student_fees` where `id` = 14502 limit 1", "type": "query", "params": [], "bindings": [14502], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.321376, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 57.135, "width_percent": 0.203}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14502", "type": "query", "params": [], "bindings": [14502], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.32307, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 57.338, "width_percent": 0.387}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 83 limit 1", "type": "query", "params": [], "bindings": [1, 83], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3250332, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1016", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1016", "ajax": false, "filename": "StudentFeesController.php", "line": "1016"}, "connection": "schola", "explain": null, "start_percent": 57.725, "width_percent": 0.381}, {"sql": "select count(*) as aggregate from `e_invoice_guardian` where `guardian_id` = 59 and `school_id` = 1 and (`name` is null or `name` = '' or `ic_no` is null or `ic_no` = '' or `tax_identification_number` is null or `tax_identification_number` = '' or `address` is null or `address` = '' or `city` is null or `city` = '' or `postal_code` is null or `postal_code` = '' or `country` is null or `country` = '' or `state` is null or `state` = '')", "type": "query", "params": [], "bindings": [59, 1, "", "", "", "", "", "", "", ""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.326859, "duration": 0.00289, "duration_str": "2.89ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1039", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1039", "ajax": false, "filename": "StudentFeesController.php", "line": "1039"}, "connection": "schola", "explain": null, "start_percent": 58.106, "width_percent": 1.722}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 83 limit 1", "type": "query", "params": [], "bindings": [1, 83], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.331221, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1045", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1045", "ajax": false, "filename": "StudentFeesController.php", "line": "1045"}, "connection": "schola", "explain": null, "start_percent": 59.828, "width_percent": 0.423}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14501 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14501], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.332988, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 60.251, "width_percent": 0.429}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` = *********", "type": "query", "params": [], "bindings": [1, *********], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.334736, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 60.68, "width_percent": 0.203}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14501 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14501, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.336387, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 60.883, "width_percent": 0.423}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14501 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14501], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.338623, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 61.306, "width_percent": 0.316}, {"sql": "select * from `student_fees` where `id` = 14501 limit 1", "type": "query", "params": [], "bindings": [14501], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3401718, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 61.622, "width_percent": 0.25}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14501", "type": "query", "params": [], "bindings": [14501], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3413692, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 61.872, "width_percent": 0.167}, {"sql": "select sum(`amount`) as aggregate from `student_fees_paids` where `student_fees_id` in (14170, 14148)", "type": "query", "params": [], "bindings": [14170, 14148], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 977}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.342576, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:977", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 977}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=977", "ajax": false, "filename": "StudentFeesController.php", "line": "977"}, "connection": "schola", "explain": null, "start_percent": 62.039, "width_percent": 0.268}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3445652, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1016", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1016", "ajax": false, "filename": "StudentFeesController.php", "line": "1016"}, "connection": "schola", "explain": null, "start_percent": 62.307, "width_percent": 0.262}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.346113, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1045", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1045", "ajax": false, "filename": "StudentFeesController.php", "line": "1045"}, "connection": "schola", "explain": null, "start_percent": 62.569, "width_percent": 0.22}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14500 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14500], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.347454, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 62.79, "width_percent": 0.28}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` = *********", "type": "query", "params": [], "bindings": [1, *********], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.348821, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 63.07, "width_percent": 0.149}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14500 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14500, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.350136, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 63.219, "width_percent": 0.512}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14500 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14500], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.354258, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 63.731, "width_percent": 0.405}, {"sql": "select * from `student_fees` where `id` = 14500 limit 1", "type": "query", "params": [], "bindings": [14500], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.35606, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 64.136, "width_percent": 0.393}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14500", "type": "query", "params": [], "bindings": [14500], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.358017, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 64.53, "width_percent": 0.399}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.360076, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1016", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1016", "ajax": false, "filename": "StudentFeesController.php", "line": "1016"}, "connection": "schola", "explain": null, "start_percent": 64.929, "width_percent": 1.078}, {"sql": "select count(*) as aggregate from `e_invoice_guardian` where `guardian_id` = 10365 and `school_id` = 1 and (`name` is null or `name` = '' or `ic_no` is null or `ic_no` = '' or `tax_identification_number` is null or `tax_identification_number` = '' or `address` is null or `address` = '' or `city` is null or `city` = '' or `postal_code` is null or `postal_code` = '' or `country` is null or `country` = '' or `state` is null or `state` = '')", "type": "query", "params": [], "bindings": [10365, 1, "", "", "", "", "", "", "", ""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.362966, "duration": 0.00316, "duration_str": "3.16ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1039", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1039", "ajax": false, "filename": "StudentFeesController.php", "line": "1039"}, "connection": "schola", "explain": null, "start_percent": 66.007, "width_percent": 1.883}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.367332, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1045", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1045", "ajax": false, "filename": "StudentFeesController.php", "line": "1045"}, "connection": "schola", "explain": null, "start_percent": 67.89, "width_percent": 1.162}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14499 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14499], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.371182, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 69.052, "width_percent": 0.661}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` = *********", "type": "query", "params": [], "bindings": [1, *********], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.373635, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 69.713, "width_percent": 0.28}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14499 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14499, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.375221, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 69.993, "width_percent": 0.399}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14499 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14499], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3796868, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 70.393, "width_percent": 0.352}, {"sql": "select * from `student_fees` where `id` = 14499 limit 1", "type": "query", "params": [], "bindings": [14499], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.381402, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 70.744, "width_percent": 0.292}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14499", "type": "query", "params": [], "bindings": [14499], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3828611, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 71.036, "width_percent": 0.286}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3850892, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1016", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1016", "ajax": false, "filename": "StudentFeesController.php", "line": "1016"}, "connection": "schola", "explain": null, "start_percent": 71.322, "width_percent": 1.227}, {"sql": "select count(*) as aggregate from `e_invoice_guardian` where `guardian_id` = 10365 and `school_id` = 1 and (`name` is null or `name` = '' or `ic_no` is null or `ic_no` = '' or `tax_identification_number` is null or `tax_identification_number` = '' or `address` is null or `address` = '' or `city` is null or `city` = '' or `postal_code` is null or `postal_code` = '' or `country` is null or `country` = '' or `state` is null or `state` = '')", "type": "query", "params": [], "bindings": [10365, 1, "", "", "", "", "", "", "", ""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.388647, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1039", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1039", "ajax": false, "filename": "StudentFeesController.php", "line": "1039"}, "connection": "schola", "explain": null, "start_percent": 72.55, "width_percent": 2.062}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.394087, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1045", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1045", "ajax": false, "filename": "StudentFeesController.php", "line": "1045"}, "connection": "schola", "explain": null, "start_percent": 74.611, "width_percent": 1.233}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14498 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14498], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.39722, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 75.845, "width_percent": 0.358}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` = *********", "type": "query", "params": [], "bindings": [1, *********], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.40006, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 76.202, "width_percent": 0.322}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14498 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14498, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.401971, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 76.524, "width_percent": 0.375}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14498 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14498], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.406158, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 76.899, "width_percent": 0.566}, {"sql": "select * from `student_fees` where `id` = 14498 limit 1", "type": "query", "params": [], "bindings": [14498], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4083269, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 77.465, "width_percent": 0.328}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14498", "type": "query", "params": [], "bindings": [14498], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.409842, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 77.793, "width_percent": 0.358}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.411977, "duration": 0.00296, "duration_str": "2.96ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1016", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1016", "ajax": false, "filename": "StudentFeesController.php", "line": "1016"}, "connection": "schola", "explain": null, "start_percent": 78.151, "width_percent": 1.764}, {"sql": "select count(*) as aggregate from `e_invoice_guardian` where `guardian_id` = 10365 and `school_id` = 1 and (`name` is null or `name` = '' or `ic_no` is null or `ic_no` = '' or `tax_identification_number` is null or `tax_identification_number` = '' or `address` is null or `address` = '' or `city` is null or `city` = '' or `postal_code` is null or `postal_code` = '' or `country` is null or `country` = '' or `state` is null or `state` = '')", "type": "query", "params": [], "bindings": [10365, 1, "", "", "", "", "", "", "", ""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4162202, "duration": 0.0034300000000000003, "duration_str": "3.43ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1039", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1039", "ajax": false, "filename": "StudentFeesController.php", "line": "1039"}, "connection": "schola", "explain": null, "start_percent": 79.914, "width_percent": 2.044}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4214082, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1045", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1045", "ajax": false, "filename": "StudentFeesController.php", "line": "1045"}, "connection": "schola", "explain": null, "start_percent": 81.958, "width_percent": 1.12}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14497 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14497], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.424252, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 83.078, "width_percent": 0.328}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4257, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 83.406, "width_percent": 0.155}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14497 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14497, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.427381, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 83.561, "width_percent": 0.369}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14497 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14497], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.431195, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 83.93, "width_percent": 0.369}, {"sql": "select * from `student_fees` where `id` = 14497 limit 1", "type": "query", "params": [], "bindings": [14497], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4332888, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 84.3, "width_percent": 0.465}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14497", "type": "query", "params": [], "bindings": [14497], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4351041, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 84.764, "width_percent": 0.304}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.436871, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1016", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1016", "ajax": false, "filename": "StudentFeesController.php", "line": "1016"}, "connection": "schola", "explain": null, "start_percent": 85.068, "width_percent": 0.387}, {"sql": "select count(*) as aggregate from `e_invoice_guardian` where `guardian_id` = 8 and `school_id` = 1 and (`name` is null or `name` = '' or `ic_no` is null or `ic_no` = '' or `tax_identification_number` is null or `tax_identification_number` = '' or `address` is null or `address` = '' or `city` is null or `city` = '' or `postal_code` is null or `postal_code` = '' or `country` is null or `country` = '' or `state` is null or `state` = '')", "type": "query", "params": [], "bindings": [8, 1, "", "", "", "", "", "", "", ""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4387271, "duration": 0.00325, "duration_str": "3.25ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1039", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1039", "ajax": false, "filename": "StudentFeesController.php", "line": "1039"}, "connection": "schola", "explain": null, "start_percent": 85.456, "width_percent": 1.936}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.44307, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1045", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1045", "ajax": false, "filename": "StudentFeesController.php", "line": "1045"}, "connection": "schola", "explain": null, "start_percent": 87.392, "width_percent": 0.34}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14496 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14496], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4445379, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 87.732, "width_percent": 0.226}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` = *********", "type": "query", "params": [], "bindings": [1, *********], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.445778, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 87.958, "width_percent": 0.143}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14496 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14496, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.447033, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 88.101, "width_percent": 0.608}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14496 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14496], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4510949, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 88.709, "width_percent": 0.346}, {"sql": "select * from `student_fees` where `id` = 14496 limit 1", "type": "query", "params": [], "bindings": [14496], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.452848, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 89.054, "width_percent": 0.238}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14496", "type": "query", "params": [], "bindings": [14496], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.454498, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 89.293, "width_percent": 0.512}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4568222, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1016", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1016", "ajax": false, "filename": "StudentFeesController.php", "line": "1016"}, "connection": "schola", "explain": null, "start_percent": 89.805, "width_percent": 1.007}, {"sql": "select count(*) as aggregate from `e_invoice_guardian` where `guardian_id` = 10365 and `school_id` = 1 and (`name` is null or `name` = '' or `ic_no` is null or `ic_no` = '' or `tax_identification_number` is null or `tax_identification_number` = '' or `address` is null or `address` = '' or `city` is null or `city` = '' or `postal_code` is null or `postal_code` = '' or `country` is null or `country` = '' or `state` is null or `state` = '')", "type": "query", "params": [], "bindings": [10365, 1, "", "", "", "", "", "", "", ""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4596949, "duration": 0.00318, "duration_str": "3.18ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1039", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1039", "ajax": false, "filename": "StudentFeesController.php", "line": "1039"}, "connection": "schola", "explain": null, "start_percent": 90.812, "width_percent": 1.895}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.463942, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1045", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1045", "ajax": false, "filename": "StudentFeesController.php", "line": "1045"}, "connection": "schola", "explain": null, "start_percent": 92.707, "width_percent": 0.924}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14495 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14495], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.466303, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 93.63, "width_percent": 0.209}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` = *********", "type": "query", "params": [], "bindings": [1, *********], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.467516, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 93.839, "width_percent": 0.191}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14495 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14495, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4691138, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 94.03, "width_percent": 0.352}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14495 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14495], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.472466, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 94.381, "width_percent": 0.495}, {"sql": "select * from `student_fees` where `id` = 14495 limit 1", "type": "query", "params": [], "bindings": [14495], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4748201, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 94.876, "width_percent": 0.602}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14495", "type": "query", "params": [], "bindings": [14495], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4768279, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 95.478, "width_percent": 0.328}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = ? and `students`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.478706, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 95.805, "width_percent": 1.061}, {"sql": "select count(*) as aggregate from `e_invoice_guardian` where `guardian_id` = ? and `school_id` = ? and (`name` is null or `name` = ? or `ic_no` is null or `ic_no` = ? or `tax_identification_number` is null or `tax_identification_number` = ? or `address` is null or `address` = ? or `city` is null or `city` = ? or `postal_code` is null or `postal_code` = ? or `country` is null or `country` = ? or `state` is null or `state` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.480946, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 96.866, "width_percent": 2.062}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = ? and `students`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4847488, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 98.927, "width_percent": 1.073}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 13675, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 227, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\StudentFeesDetail": {"value": 24, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FStudentFeesDetail.php&line=1", "ajax": false, "filename": "StudentFeesDetail.php", "line": "?"}}, "App\\Models\\StudentFee": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FStudentFee.php&line=1", "ajax": false, "filename": "StudentFee.php", "line": "?"}}, "App\\Models\\User": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Students": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FStudents.php&line=1", "ajax": false, "filename": "Students.php", "line": "?"}}, "App\\Models\\ClassSchool": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FClassSchool.php&line=1", "ajax": false, "filename": "ClassSchool.php", "line": "?"}}, "App\\Models\\School": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FSchool.php&line=1", "ajax": false, "filename": "School.php", "line": "?"}}, "App\\Models\\Mediums": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FMediums.php&line=1", "ajax": false, "filename": "Mediums.php", "line": "?"}}}, "count": 13947, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => fees-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1806467063 data-indent-pad=\"  \"><span class=sf-dump-note>fees-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">fees-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1806467063\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.206893, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "https://schola.test/student-fees/1?end_date=&limit=10&offset=0&order=desc&search=&show_deleted=0&sor...", "action_name": "student-fees.show", "controller_action": "App\\Http\\Controllers\\StudentFeesController@show", "uri": "GET student-fees/{student_fee}", "controller": "App\\Http\\Controllers\\StudentFeesController@show<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=476\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=476\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/StudentFeesController.php:476-1054</a>", "middleware": "web, Role, auth, checkSchoolStatus, status, language", "duration": "1.61s", "peak_memory": "94MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1417471992 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>sort</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n  \"<span class=sf-dump-key>order</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n  \"<span class=sf-dump-key>offset</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>start_date</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>end_date</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>show_deleted</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1417471992\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-843561360 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-843561360\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-887106783 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">schola.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">qzZAZCbyxouAflTIwxgtbSqzkAL1GsMdrfVvou3B</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">https://schola.test/student-fees</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"742 characters\">XSRF-TOKEN=eyJpdiI6IkpTMER2UDhNc0Z1eUhmQXFHaVdsdkE9PSIsInZhbHVlIjoiNEVhVmhiSHVXR0hDSHdaMTVpcENqZzcxOWdxVWc0bzAzdTQ0T2dwei9PS2NPbFVJVjgrS1pUQkdQdUZia2d0VU1uSTJ1YTRJMzBqRndQWWlZYUxSbk9ZbjIwSFRpRlhndXY0MVFvMTRZRXhGMVdNQVUvZWVqWHZNY2Vlc2t4SCsiLCJtYWMiOiJiZGI2Y2RmMWFkNTJkYWYyN2QzMzlmZjkwODUyNmJkYmQ2MWFjZTU1OGFjNmMyYWRhYjM4NmQ4NjJkMzU1ZTlhIiwidGFnIjoiIn0%3D; schola_saas_school_management_system_session=eyJpdiI6ImZQMGRSRFNmWWhoTUs4Y0lSb2hDaHc9PSIsInZhbHVlIjoiNm5lMUxkS2kxVnoyeU5EeGxkQVplRVcxT1Z5amN0dVkwR0dmcnkwTEZvQU5hbjV4bWp4MGtNSGh3d1piZS9VYmFPZkFQaXVZRGFQZzBrNDJ5OGYzOHZwc0tOdHYvYWppREZZTlBJR1JRdURReVZZenVuMURheGRhc3ZSbTh4Q1AiLCJtYWMiOiJkYmM1ZDM3ZTRkMDg2NWI5MjJhOWVhM2NlODg2NjkyMWZjNDkwMWE4MzM0OTU0ODU0ZTk1NjA2MTdlY2NhMzhiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-887106783\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qzZAZCbyxouAflTIwxgtbSqzkAL1GsMdrfVvou3B</span>\"\n  \"<span class=sf-dump-key>schola_saas_school_management_system_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ngwskcSF7OPJhEVQnobXQuptusCBAMo2pYQsya27</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 01:30:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-371769311 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qzZAZCbyxouAflTIwxgtbSqzkAL1GsMdrfVvou3B</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"63 characters\">https://schola.test/student-fees/paid/receipt-pdf/14505/invoice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>landing_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>language</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Language\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Language</span></span> {<a class=sf-dump-ref>#3448</a><samp data-depth=2 class=sf-dump-compact>\n    #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">languages</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n    +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n    #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n    +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n    +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n    +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">English</span>\"\n      \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"7 characters\">en.json</span>\"\n      \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>is_rtl</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n      \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">English</span>\"\n      \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"7 characters\">en.json</span>\"\n      \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>is_rtl</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n      \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n    +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n    +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">code</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">is_rtl</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n    </samp>]\n  </samp>}\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1749172762</span>\n  </samp>]\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-371769311\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://schola.test/student-fees/1?end_date=&limit=10&offset=0&order=desc&search=&show_deleted=0&sor...", "action_name": "student-fees.show", "controller_action": "App\\Http\\Controllers\\StudentFeesController@show"}, "badge": null}}