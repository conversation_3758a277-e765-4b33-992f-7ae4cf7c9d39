{"__meta": {"id": "01JX1DSECNY5W6PZC5M5SK92MS", "datetime": "2025-06-06 09:30:51", "utime": **********.1579, "method": "GET", "uri": "/student-fees/1?limit=10&sort=id&order=desc&offset=0&search=&start_date=&end_date=&show_deleted=0", "ip": "127.0.0.1"}, "messages": {"count": 8, "messages": [{"message": "[09:30:50] LOG.warning: Optional parameter $includeSignature declared before required parameter $invoiceTypeCode is implicitly treated as a required parameter in D:\\laragon\\www\\schola\\app\\Services\\EInvoiceFormatService.php on line 748", "message_html": null, "is_string": false, "label": "warning", "time": **********.191636, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:50] LOG.warning: strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated in D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php on line 928", "message_html": null, "is_string": false, "label": "warning", "time": **********.977123, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:50] LOG.warning: strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated in D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php on line 928", "message_html": null, "is_string": false, "label": "warning", "time": **********.99262, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:51] LOG.warning: strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated in D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php on line 928", "message_html": null, "is_string": false, "label": "warning", "time": **********.020718, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:51] LOG.warning: strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated in D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php on line 928", "message_html": null, "is_string": false, "label": "warning", "time": **********.036408, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:51] LOG.warning: strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated in D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php on line 928", "message_html": null, "is_string": false, "label": "warning", "time": **********.106171, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:51] LOG.warning: strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated in D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php on line 928", "message_html": null, "is_string": false, "label": "warning", "time": **********.12335, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:51] LOG.warning: strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated in D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php on line 928", "message_html": null, "is_string": false, "label": "warning", "time": **********.142007, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1749173449.889816, "end": **********.157924, "duration": 1.2681078910827637, "duration_str": "1.27s", "measures": [{"label": "Booting", "start": 1749173449.889816, "relative_start": 0, "end": **********.13932, "relative_end": **********.13932, "duration": 0.*****************, "duration_str": "250ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.139329, "relative_start": 0.****************, "end": **********.157925, "relative_end": 9.5367431640625e-07, "duration": 1.****************, "duration_str": "1.02s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.15983, "relative_start": 0.*****************, "end": **********.167808, "relative_end": **********.167808, "duration": 0.007977962493896484, "duration_str": "7.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.15489, "relative_start": 1.****************, "end": **********.155372, "relative_end": **********.155372, "duration": 0.0004818439483642578, "duration_str": "482μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "90MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.10", "Environment": "local", "Debug Mode": "Enabled", "URL": "schola.one", "Timezone": "Asia/Kuala_Lumpur", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 103, "nb_statements": 103, "nb_visible_statements": 103, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.14977999999999994, "accumulated_duration_str": "150ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 3 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `users` where `id` = 2 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.202478, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "schola", "explain": null, "start_percent": 0, "width_percent": 0.387}, {"sql": "select * from `schools` where `schools`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 13}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "Role", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckRole.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2084348, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "checkSchoolStatus:13", "source": {"index": 22, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FMiddleware%2FCheckSchoolStatus.php&line=13", "ajax": false, "filename": "CheckSchoolStatus.php", "line": "13"}, "connection": "schola", "explain": null, "start_percent": 0.387, "width_percent": 0.274}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (2) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 27}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "Role", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckRole.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.213813, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "schola", "explain": null, "start_percent": 0.661, "width_percent": 0.454}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 304}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 174}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 175}], "start": **********.2201538, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:266", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=266", "ajax": false, "filename": "PermissionRegistrar.php", "line": "266"}, "connection": "schola", "explain": null, "start_percent": 1.115, "width_percent": 0.741}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 613, 1259, 1260, 1261, 1262, 1263, 1265, 1266, 1267, 1268, 1269, 1270, 1271, 1272, 1273)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 304}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 174}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 175}], "start": **********.22801, "duration": 0.06149, "duration_str": "61.49ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:266", "source": {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=266", "ajax": false, "filename": "PermissionRegistrar.php", "line": "266"}, "connection": "schola", "explain": null, "start_percent": 1.856, "width_percent": 41.054}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 2 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [2, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.941371, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "schola", "explain": null, "start_percent": 42.91, "width_percent": 0.507}, {"sql": "select count(*) as aggregate from `student_fees` where `school_id` = 1 and `student_fees`.`school_id` = 1 and (`student_fees`.`deleted_at` is null)", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 804}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9461682, "duration": 0.0052, "duration_str": "5.2ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:804", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 804}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=804", "ajax": false, "filename": "StudentFeesController.php", "line": "804"}, "connection": "schola", "explain": null, "start_percent": 43.417, "width_percent": 3.472}, {"sql": "select `student_fees`.* from `student_fees` where `school_id` = 1 and `student_fees`.`school_id` = 1 and (`student_fees`.`deleted_at` is null) order by `id` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.952226, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:806", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=806", "ajax": false, "filename": "StudentFeesController.php", "line": "806"}, "connection": "schola", "explain": null, "start_percent": 46.889, "width_percent": 0.461}, {"sql": "select * from `students` where `students`.`id` in (1, 83, 4868)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.954176, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:806", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=806", "ajax": false, "filename": "StudentFeesController.php", "line": "806"}, "connection": "schola", "explain": null, "start_percent": 47.349, "width_percent": 0.608}, {"sql": "select * from `users` where `users`.`id` in (5, 131, 10366)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.956533, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:806", "source": {"index": 25, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=806", "ajax": false, "filename": "StudentFeesController.php", "line": "806"}, "connection": "schola", "explain": null, "start_percent": 47.957, "width_percent": 0.334}, {"sql": "select `id`, `name`, `stream_id`, `medium_id` from `classes` where `classes`.`id` in (343, 407)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.958334, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:806", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=806", "ajax": false, "filename": "StudentFeesController.php", "line": "806"}, "connection": "schola", "explain": null, "start_percent": 48.291, "width_percent": 0.327}, {"sql": "select `id`, `name` from `mediums` where `mediums`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.960114, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:806", "source": {"index": 25, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=806", "ajax": false, "filename": "StudentFeesController.php", "line": "806"}, "connection": "schola", "explain": null, "start_percent": 48.618, "width_percent": 0.28}, {"sql": "select * from `student_fees_details` where `student_fees_details`.`student_fees_id` in (14495, 14496, 14497, 14498, 14499, 14500, 14501, 14502, 14504, 14505)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.963445, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:806", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 806}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=806", "ajax": false, "filename": "StudentFeesController.php", "line": "806"}, "connection": "schola", "explain": null, "start_percent": 48.898, "width_percent": 0.554}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14505 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14505], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9657829, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 49.453, "width_percent": 0.474}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` = *********", "type": "query", "params": [], "bindings": [1, *********], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.967274, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 49.927, "width_percent": 0.28}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14505 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14505, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.969146, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 50.207, "width_percent": 0.414}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14505 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14505], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9750829, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 50.621, "width_percent": 0.734}, {"sql": "select * from `student_fees` where `id` = 14505 limit 1", "type": "query", "params": [], "bindings": [14505], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9772751, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 51.355, "width_percent": 0.34}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14505", "type": "query", "params": [], "bindings": [14505], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.978478, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 51.696, "width_percent": 0.267}, {"sql": "select sum(`amount`) as aggregate from `student_fees_paids` where `student_fees_id` in (14504, 14502)", "type": "query", "params": [], "bindings": [14504, 14502], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 977}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.979713, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:977", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 977}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=977", "ajax": false, "filename": "StudentFeesController.php", "line": "977"}, "connection": "schola", "explain": null, "start_percent": 51.963, "width_percent": 0.287}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.981169, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1016", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1016", "ajax": false, "filename": "StudentFeesController.php", "line": "1016"}, "connection": "schola", "explain": null, "start_percent": 52.25, "width_percent": 0.307}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.98285, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1045", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1045", "ajax": false, "filename": "StudentFeesController.php", "line": "1045"}, "connection": "schola", "explain": null, "start_percent": 52.557, "width_percent": 0.34}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14504 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14504], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9841812, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 52.898, "width_percent": 0.414}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` = *********", "type": "query", "params": [], "bindings": [1, *********], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9855711, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 53.312, "width_percent": 0.2}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14504 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14504, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.986624, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 53.512, "width_percent": 0.267}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14504 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14504], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9910681, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 53.779, "width_percent": 0.461}, {"sql": "select * from `student_fees` where `id` = 14504 limit 1", "type": "query", "params": [], "bindings": [14504], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.992774, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 54.24, "width_percent": 0.314}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14504", "type": "query", "params": [], "bindings": [14504], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.993933, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 54.553, "width_percent": 0.28}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9953399, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1016", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1016", "ajax": false, "filename": "StudentFeesController.php", "line": "1016"}, "connection": "schola", "explain": null, "start_percent": 54.834, "width_percent": 1.215}, {"sql": "select count(*) as aggregate from `e_invoice_guardian` where `guardian_id` = 10365 and `school_id` = 1 and (`name` is null or `name` = '' or `ic_no` is null or `ic_no` = '' or `tax_identification_number` is null or `tax_identification_number` = '' or `address` is null or `address` = '' or `city` is null or `city` = '' or `postal_code` is null or `postal_code` = '' or `country` is null or `country` = '' or `state` is null or `state` = '')", "type": "query", "params": [], "bindings": [10365, 1, "", "", "", "", "", "", "", ""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9981349, "duration": 0.00248, "duration_str": "2.48ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1039", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1039", "ajax": false, "filename": "StudentFeesController.php", "line": "1039"}, "connection": "schola", "explain": null, "start_percent": 56.049, "width_percent": 1.656}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.001466, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1045", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1045", "ajax": false, "filename": "StudentFeesController.php", "line": "1045"}, "connection": "schola", "explain": null, "start_percent": 57.705, "width_percent": 1.262}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14502 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14502], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.004405, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 58.966, "width_percent": 0.487}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` = *********", "type": "query", "params": [], "bindings": [1, *********], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.005936, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 59.454, "width_percent": 0.234}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14502 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14502, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.0070322, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 59.688, "width_percent": 0.314}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14502 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14502], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.019261, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 60.001, "width_percent": 0.387}, {"sql": "select * from `student_fees` where `id` = 14502 limit 1", "type": "query", "params": [], "bindings": [14502], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.020848, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 60.389, "width_percent": 0.247}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14502", "type": "query", "params": [], "bindings": [14502], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.022004, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 60.636, "width_percent": 0.24}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 83 limit 1", "type": "query", "params": [], "bindings": [1, 83], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.023503, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1016", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1016", "ajax": false, "filename": "StudentFeesController.php", "line": "1016"}, "connection": "schola", "explain": null, "start_percent": 60.876, "width_percent": 0.561}, {"sql": "select count(*) as aggregate from `e_invoice_guardian` where `guardian_id` = 59 and `school_id` = 1 and (`name` is null or `name` = '' or `ic_no` is null or `ic_no` = '' or `tax_identification_number` is null or `tax_identification_number` = '' or `address` is null or `address` = '' or `city` is null or `city` = '' or `postal_code` is null or `postal_code` = '' or `country` is null or `country` = '' or `state` is null or `state` = '')", "type": "query", "params": [], "bindings": [59, 1, "", "", "", "", "", "", "", ""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.025521, "duration": 0.00244, "duration_str": "2.44ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1039", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1039", "ajax": false, "filename": "StudentFeesController.php", "line": "1039"}, "connection": "schola", "explain": null, "start_percent": 61.437, "width_percent": 1.629}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 83 limit 1", "type": "query", "params": [], "bindings": [1, 83], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.028891, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1045", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1045", "ajax": false, "filename": "StudentFeesController.php", "line": "1045"}, "connection": "schola", "explain": null, "start_percent": 63.066, "width_percent": 0.214}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14501 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14501], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.030222, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 63.279, "width_percent": 0.728}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` = *********", "type": "query", "params": [], "bindings": [1, *********], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.0323532, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 64.007, "width_percent": 0.307}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14501 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14501, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.033712, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 64.314, "width_percent": 0.267}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14501 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14501], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.035285, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 64.581, "width_percent": 0.247}, {"sql": "select * from `student_fees` where `id` = 14501 limit 1", "type": "query", "params": [], "bindings": [14501], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.0365238, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 64.828, "width_percent": 0.28}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14501", "type": "query", "params": [], "bindings": [14501], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.037956, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 65.109, "width_percent": 0.441}, {"sql": "select sum(`amount`) as aggregate from `student_fees_paids` where `student_fees_id` in (14170, 14148)", "type": "query", "params": [], "bindings": [14170, 14148], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 977}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.039614, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:977", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 977}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=977", "ajax": false, "filename": "StudentFeesController.php", "line": "977"}, "connection": "schola", "explain": null, "start_percent": 65.549, "width_percent": 0.247}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.041039, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1016", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1016", "ajax": false, "filename": "StudentFeesController.php", "line": "1016"}, "connection": "schola", "explain": null, "start_percent": 65.797, "width_percent": 0.207}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.0421941, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1045", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1045", "ajax": false, "filename": "StudentFeesController.php", "line": "1045"}, "connection": "schola", "explain": null, "start_percent": 66.003, "width_percent": 0.187}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14500 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14500], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.043249, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 66.19, "width_percent": 0.234}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` = *********", "type": "query", "params": [], "bindings": [1, *********], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.044648, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 66.424, "width_percent": 0.407}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14500 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14500, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.046282, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 66.831, "width_percent": 0.374}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14500 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14500], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.049317, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 67.205, "width_percent": 0.274}, {"sql": "select * from `student_fees` where `id` = 14500 limit 1", "type": "query", "params": [], "bindings": [14500], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.0505779, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 67.479, "width_percent": 0.274}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14500", "type": "query", "params": [], "bindings": [14500], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.05218, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 67.753, "width_percent": 0.501}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.0541, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1016", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1016", "ajax": false, "filename": "StudentFeesController.php", "line": "1016"}, "connection": "schola", "explain": null, "start_percent": 68.253, "width_percent": 1.082}, {"sql": "select count(*) as aggregate from `e_invoice_guardian` where `guardian_id` = 10365 and `school_id` = 1 and (`name` is null or `name` = '' or `ic_no` is null or `ic_no` = '' or `tax_identification_number` is null or `tax_identification_number` = '' or `address` is null or `address` = '' or `city` is null or `city` = '' or `postal_code` is null or `postal_code` = '' or `country` is null or `country` = '' or `state` is null or `state` = '')", "type": "query", "params": [], "bindings": [10365, 1, "", "", "", "", "", "", "", ""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.05664, "duration": 0.00262, "duration_str": "2.62ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1039", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1039", "ajax": false, "filename": "StudentFeesController.php", "line": "1039"}, "connection": "schola", "explain": null, "start_percent": 69.335, "width_percent": 1.749}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.060199, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1045", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1045", "ajax": false, "filename": "StudentFeesController.php", "line": "1045"}, "connection": "schola", "explain": null, "start_percent": 71.084, "width_percent": 1.042}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14499 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14499], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.0625038, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 72.126, "width_percent": 0.28}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` = *********", "type": "query", "params": [], "bindings": [1, *********], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.063653, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 72.406, "width_percent": 0.234}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14499 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14499, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.064865, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 72.64, "width_percent": 0.441}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14499 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14499], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.068135, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 73.081, "width_percent": 0.354}, {"sql": "select * from `student_fees` where `id` = 14499 limit 1", "type": "query", "params": [], "bindings": [14499], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.069466, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 73.434, "width_percent": 0.234}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14499", "type": "query", "params": [], "bindings": [14499], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.070498, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 73.668, "width_percent": 0.2}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.071777, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1016", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1016", "ajax": false, "filename": "StudentFeesController.php", "line": "1016"}, "connection": "schola", "explain": null, "start_percent": 73.868, "width_percent": 1.162}, {"sql": "select count(*) as aggregate from `e_invoice_guardian` where `guardian_id` = 10365 and `school_id` = 1 and (`name` is null or `name` = '' or `ic_no` is null or `ic_no` = '' or `tax_identification_number` is null or `tax_identification_number` = '' or `address` is null or `address` = '' or `city` is null or `city` = '' or `postal_code` is null or `postal_code` = '' or `country` is null or `country` = '' or `state` is null or `state` = '')", "type": "query", "params": [], "bindings": [10365, 1, "", "", "", "", "", "", "", ""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.074533, "duration": 0.00249, "duration_str": "2.49ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1039", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1039", "ajax": false, "filename": "StudentFeesController.php", "line": "1039"}, "connection": "schola", "explain": null, "start_percent": 75.03, "width_percent": 1.662}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.077862, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1045", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1045", "ajax": false, "filename": "StudentFeesController.php", "line": "1045"}, "connection": "schola", "explain": null, "start_percent": 76.692, "width_percent": 1.242}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14498 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14498], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.080643, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 77.934, "width_percent": 0.361}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` = *********", "type": "query", "params": [], "bindings": [1, *********], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.081968, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 78.295, "width_percent": 0.234}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14498 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14498, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.0830672, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 78.529, "width_percent": 0.3}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14498 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14498], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.086001, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 78.829, "width_percent": 0.481}, {"sql": "select * from `student_fees` where `id` = 14498 limit 1", "type": "query", "params": [], "bindings": [14498], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.087787, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 79.31, "width_percent": 0.387}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14498", "type": "query", "params": [], "bindings": [14498], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.089082, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 79.697, "width_percent": 0.234}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.090407, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1016", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1016", "ajax": false, "filename": "StudentFeesController.php", "line": "1016"}, "connection": "schola", "explain": null, "start_percent": 79.931, "width_percent": 0.948}, {"sql": "select count(*) as aggregate from `e_invoice_guardian` where `guardian_id` = 10365 and `school_id` = 1 and (`name` is null or `name` = '' or `ic_no` is null or `ic_no` = '' or `tax_identification_number` is null or `tax_identification_number` = '' or `address` is null or `address` = '' or `city` is null or `city` = '' or `postal_code` is null or `postal_code` = '' or `country` is null or `country` = '' or `state` is null or `state` = '')", "type": "query", "params": [], "bindings": [10365, 1, "", "", "", "", "", "", "", ""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.092731, "duration": 0.00287, "duration_str": "2.87ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1039", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1039", "ajax": false, "filename": "StudentFeesController.php", "line": "1039"}, "connection": "schola", "explain": null, "start_percent": 80.879, "width_percent": 1.916}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.096518, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1045", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1045", "ajax": false, "filename": "StudentFeesController.php", "line": "1045"}, "connection": "schola", "explain": null, "start_percent": 82.795, "width_percent": 0.955}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14497 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14497], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.0987082, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 83.749, "width_percent": 0.441}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.100741, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 84.19, "width_percent": 0.287}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14497 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14497, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.102091, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 84.477, "width_percent": 0.354}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14497 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14497], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1049361, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 84.831, "width_percent": 0.314}, {"sql": "select * from `student_fees` where `id` = 14497 limit 1", "type": "query", "params": [], "bindings": [14497], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.10635, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 85.145, "width_percent": 0.32}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14497", "type": "query", "params": [], "bindings": [14497], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.107877, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 85.465, "width_percent": 0.401}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.109485, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1016", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1016", "ajax": false, "filename": "StudentFeesController.php", "line": "1016"}, "connection": "schola", "explain": null, "start_percent": 85.866, "width_percent": 0.374}, {"sql": "select count(*) as aggregate from `e_invoice_guardian` where `guardian_id` = 8 and `school_id` = 1 and (`name` is null or `name` = '' or `ic_no` is null or `ic_no` = '' or `tax_identification_number` is null or `tax_identification_number` = '' or `address` is null or `address` = '' or `city` is null or `city` = '' or `postal_code` is null or `postal_code` = '' or `country` is null or `country` = '' or `state` is null or `state` = '')", "type": "query", "params": [], "bindings": [8, 1, "", "", "", "", "", "", "", ""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.110921, "duration": 0.00246, "duration_str": "2.46ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1039", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1039", "ajax": false, "filename": "StudentFeesController.php", "line": "1039"}, "connection": "schola", "explain": null, "start_percent": 86.24, "width_percent": 1.642}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1147358, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1045", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1045", "ajax": false, "filename": "StudentFeesController.php", "line": "1045"}, "connection": "schola", "explain": null, "start_percent": 87.882, "width_percent": 0.501}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14496 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14496], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.116328, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 88.383, "width_percent": 0.3}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` = *********", "type": "query", "params": [], "bindings": [1, *********], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.117555, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 88.683, "width_percent": 0.227}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14496 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14496, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1186512, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 88.91, "width_percent": 0.307}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14496 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14496], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1218462, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 89.218, "width_percent": 0.421}, {"sql": "select * from `student_fees` where `id` = 14496 limit 1", "type": "query", "params": [], "bindings": [14496], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.123482, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 89.638, "width_percent": 0.314}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14496", "type": "query", "params": [], "bindings": [14496], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.124696, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 89.952, "width_percent": 0.254}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.126101, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1016", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1016}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1016", "ajax": false, "filename": "StudentFeesController.php", "line": "1016"}, "connection": "schola", "explain": null, "start_percent": 90.206, "width_percent": 1.088}, {"sql": "select count(*) as aggregate from `e_invoice_guardian` where `guardian_id` = 10365 and `school_id` = 1 and (`name` is null or `name` = '' or `ic_no` is null or `ic_no` = '' or `tax_identification_number` is null or `tax_identification_number` = '' or `address` is null or `address` = '' or `city` is null or `city` = '' or `postal_code` is null or `postal_code` = '' or `country` is null or `country` = '' or `state` is null or `state` = '')", "type": "query", "params": [], "bindings": [10365, 1, "", "", "", "", "", "", "", ""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.128844, "duration": 0.00259, "duration_str": "2.59ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1039", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1039}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1039", "ajax": false, "filename": "StudentFeesController.php", "line": "1039"}, "connection": "schola", "explain": null, "start_percent": 91.294, "width_percent": 1.729}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = 1 and `students`.`id` = 4868 limit 1", "type": "query", "params": [], "bindings": [1, 4868], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.132341, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:1045", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 1045}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=1045", "ajax": false, "filename": "StudentFeesController.php", "line": "1045"}, "connection": "schola", "explain": null, "start_percent": 93.023, "width_percent": 0.981}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = 14495 order by `id` desc limit 1", "type": "query", "params": [], "bindings": [14495], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1347432, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:819", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 819}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=819", "ajax": false, "filename": "StudentFeesController.php", "line": "819"}, "connection": "schola", "explain": null, "start_percent": 94.005, "width_percent": 0.534}, {"sql": "select `student_fees_id` from `student_fees_consolidate` where `school_id` = 1 and `reference_uid` = *********", "type": "query", "params": [], "bindings": [1, *********], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.13651, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:826", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 826}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=826", "ajax": false, "filename": "StudentFeesController.php", "line": "826"}, "connection": "schola", "explain": null, "start_percent": 94.539, "width_percent": 0.287}, {"sql": "select * from `student_fees_details` where `student_fees_id` = 14495 and `fees_type_name` not in ('Early Discount', 'Overdue Fees')", "type": "query", "params": [], "bindings": [14495, "Early Discount", "Overdue Fees"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1377351, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:832", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 832}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=832", "ajax": false, "filename": "StudentFeesController.php", "line": "832"}, "connection": "schola", "explain": null, "start_percent": 94.826, "width_percent": 0.314}, {"sql": "select `mode`, MAX(student_fees_paids.status) as fees_status, `date` as `fee_paid_date`, `is_fully_paid` from `student_fees_paids` where `student_fees_paids`.`student_fees_id` = 14495 group by `mode`, `date`, `is_fully_paid` limit 1", "type": "query", "params": [], "bindings": [14495], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.140537, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:913", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 913}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=913", "ajax": false, "filename": "StudentFeesController.php", "line": "913"}, "connection": "schola", "explain": null, "start_percent": 95.14, "width_percent": 0.307}, {"sql": "select * from `student_fees` where `id` = 14495 limit 1", "type": "query", "params": [], "bindings": [14495], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.142206, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:929", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=929", "ajax": false, "filename": "StudentFeesController.php", "line": "929"}, "connection": "schola", "explain": null, "start_percent": 95.447, "width_percent": 0.407}, {"sql": "SELECT * FROM student_fees_paids WHERE student_fees_id = 14495", "type": "query", "params": [], "bindings": [14495], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.143629, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:930", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 930}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=930", "ajax": false, "filename": "StudentFeesController.php", "line": "930"}, "connection": "schola", "explain": null, "start_percent": 95.854, "width_percent": 0.294}, {"sql": "select `e_invoice_guardian`.`guardian_id`, `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = ? and `students`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1450322, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 96.148, "width_percent": 1.021}, {"sql": "select count(*) as aggregate from `e_invoice_guardian` where `guardian_id` = ? and `school_id` = ? and (`name` is null or `name` = ? or `ic_no` is null or `ic_no` = ? or `tax_identification_number` is null or `tax_identification_number` = ? or `address` is null or `address` = ? or `city` is null or `city` = ? or `postal_code` is null or `postal_code` = ? or `country` is null or `country` = ? or `state` is null or `state` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.146824, "duration": 0.00269, "duration_str": "2.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 97.169, "width_percent": 1.796}, {"sql": "select `e_invoice_guardian`.`sql_code` from `e_invoice_guardian` inner join `students` on `students`.`guardian_id` = `e_invoice_guardian`.`guardian_id` where `e_invoice_guardian`.`school_id` = ? and `students`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.149792, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "schola", "explain": null, "start_percent": 98.965, "width_percent": 1.035}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 13675, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 227, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\StudentFeesDetail": {"value": 24, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FStudentFeesDetail.php&line=1", "ajax": false, "filename": "StudentFeesDetail.php", "line": "?"}}, "App\\Models\\StudentFee": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FStudentFee.php&line=1", "ajax": false, "filename": "StudentFee.php", "line": "?"}}, "App\\Models\\User": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Students": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FStudents.php&line=1", "ajax": false, "filename": "Students.php", "line": "?"}}, "App\\Models\\ClassSchool": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FClassSchool.php&line=1", "ajax": false, "filename": "ClassSchool.php", "line": "?"}}, "App\\Models\\School": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FSchool.php&line=1", "ajax": false, "filename": "School.php", "line": "?"}}, "App\\Models\\Mediums": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FMediums.php&line=1", "ajax": false, "filename": "Mediums.php", "line": "?"}}}, "count": 13947, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => fees-list,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2124198333 data-indent-pad=\"  \"><span class=sf-dump-note>fees-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">fees-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2124198333\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.945244, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "https://schola.test/student-fees/1?end_date=&limit=10&offset=0&order=desc&search=&show_deleted=0&sor...", "action_name": "student-fees.show", "controller_action": "App\\Http\\Controllers\\StudentFeesController@show", "uri": "GET student-fees/{student_fee}", "controller": "App\\Http\\Controllers\\StudentFeesController@show<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=476\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=476\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/StudentFeesController.php:476-1054</a>", "middleware": "web, Role, auth, checkSchoolStatus, status, language", "duration": "1.28s", "peak_memory": "94MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1217518348 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>sort</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n  \"<span class=sf-dump-key>order</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n  \"<span class=sf-dump-key>offset</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>start_date</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>end_date</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>show_deleted</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1217518348\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-46420148 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-46420148\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1224495499 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">schola.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">qzZAZCbyxouAflTIwxgtbSqzkAL1GsMdrfVvou3B</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">https://schola.test/student-fees</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"742 characters\">XSRF-TOKEN=eyJpdiI6Ilo3cUtXeThKOVVNSjBhUXpQaDhDSGc9PSIsInZhbHVlIjoiQ2dqVE1tcDZRc21WMUJDOG1WWkRScFJybmNBNG5CVkV1WFNzT0ZZdVI1Q2tvaEVUb29Qc0NReE9Ld2NncWs1dHlEallpaHBPM2prR1UrZHg5US8xNzJNTVRPZStPUERYK1BQVTBobGJHb0FSank4Y1AvWlRqbWk3YjdUYm9tL0EiLCJtYWMiOiIxMzI5YTdjNjljNzI3MzczMzJiNzIzZjdjMjY1NzExZjk1NDg3ZThkOWY0YWY1NzE0YmQzYjQwZDI0N2YyYTY4IiwidGFnIjoiIn0%3D; schola_saas_school_management_system_session=eyJpdiI6IkJXRkh6S0ptM0RQbTFlN01jaGYvSkE9PSIsInZhbHVlIjoiendpZ0lLbC9vbDh5VnQweWQrb1M5Z2FXQXNMMWhHM2g5anp1TGtuWXhmNFRRS2ZMTkZlNWRLUmhJd2VaRXlyVllqelBuZ05kOW5PZk9DWVZtMnphZXVka29LbExMSUFkSGJ5MFRXbzlySXErUW12RHRRVFhJa1lvUW9ZUW9CMUgiLCJtYWMiOiJlNWY4MjJhODhlMDI2ZmEyMGMxZGNjNDU2Y2U3NDQzMTRmZTJiNTQzNGQwNjcwMTVhZGI0NTE0Mzc1MDFhOWFlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1224495499\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-679632077 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qzZAZCbyxouAflTIwxgtbSqzkAL1GsMdrfVvou3B</span>\"\n  \"<span class=sf-dump-key>schola_saas_school_management_system_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ngwskcSF7OPJhEVQnobXQuptusCBAMo2pYQsya27</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-679632077\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-120497126 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 01:30:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-120497126\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-353903198 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qzZAZCbyxouAflTIwxgtbSqzkAL1GsMdrfVvou3B</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"63 characters\">https://schola.test/student-fees/paid/receipt-pdf/14505/invoice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>landing_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>language</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Language\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Language</span></span> {<a class=sf-dump-ref>#3448</a><samp data-depth=2 class=sf-dump-compact>\n    #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">languages</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n    +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n    #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n    +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n    +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n    +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">English</span>\"\n      \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"7 characters\">en.json</span>\"\n      \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>is_rtl</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n      \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">English</span>\"\n      \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"7 characters\">en.json</span>\"\n      \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>is_rtl</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n      \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n    +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n    +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">code</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">is_rtl</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n    </samp>]\n  </samp>}\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1749172762</span>\n  </samp>]\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-353903198\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://schola.test/student-fees/1?end_date=&limit=10&offset=0&order=desc&search=&show_deleted=0&sor...", "action_name": "student-fees.show", "controller_action": "App\\Http\\Controllers\\StudentFeesController@show"}, "badge": null}}